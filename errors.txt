[{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/a_main/TestTriangle.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "268435846",
	"severity": 8,
	"message": "The import tested.Triangle cannot be resolved",
	"source": "Java",
	"startLineNumber": 11,
	"startColumn": 8,
	"endLineNumber": 11,
	"endColumn": 23
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/a_main/TestTriangle.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "570425394",
	"severity": 8,
	"message": "Triangle cannot be resolved",
	"source": "Java",
	"startLineNumber": 32,
	"startColumn": 13,
	"endLineNumber": 32,
	"endColumn": 21
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/a_main/TestTriangle.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "536870973",
	"severity": 4,
	"message": "The value of the local variable count3 is not used",
	"source": "Java",
	"startLineNumber": 77,
	"startColumn": 7,
	"endLineNumber": 77,
	"endColumn": 13,
	"tags": [
		1
	]
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/a_main/TestTriangle.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "536870973",
	"severity": 4,
	"message": "The value of the local variable show1 is not used",
	"source": "Java",
	"startLineNumber": 83,
	"startColumn": 11,
	"endLineNumber": 83,
	"endColumn": 16,
	"tags": [
		1
	]
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/a_main/TestTriangle.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "536870973",
	"severity": 4,
	"message": "The value of the local variable show2 is not used",
	"source": "Java",
	"startLineNumber": 84,
	"startColumn": 11,
	"endLineNumber": 84,
	"endColumn": 16,
	"tags": [
		1
	]
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "268435846",
	"severity": 8,
	"message": "The import org.jacoco cannot be resolved",
	"source": "Java",
	"startLineNumber": 8,
	"startColumn": 8,
	"endLineNumber": 8,
	"endColumn": 18
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "268435846",
	"severity": 8,
	"message": "The import org.jacoco cannot be resolved",
	"source": "Java",
	"startLineNumber": 9,
	"startColumn": 8,
	"endLineNumber": 9,
	"endColumn": 18
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "268435846",
	"severity": 8,
	"message": "The import org.jacoco cannot be resolved",
	"source": "Java",
	"startLineNumber": 10,
	"startColumn": 8,
	"endLineNumber": 10,
	"endColumn": 18
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "268435846",
	"severity": 8,
	"message": "The import org.jacoco cannot be resolved",
	"source": "Java",
	"startLineNumber": 11,
	"startColumn": 8,
	"endLineNumber": 11,
	"endColumn": 18
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "268435846",
	"severity": 8,
	"message": "The import org.jacoco cannot be resolved",
	"source": "Java",
	"startLineNumber": 12,
	"startColumn": 8,
	"endLineNumber": 12,
	"endColumn": 18
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "268435846",
	"severity": 8,
	"message": "The import org.jacoco cannot be resolved",
	"source": "Java",
	"startLineNumber": 13,
	"startColumn": 8,
	"endLineNumber": 13,
	"endColumn": 18
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "268435846",
	"severity": 8,
	"message": "The import org.jacoco cannot be resolved",
	"source": "Java",
	"startLineNumber": 14,
	"startColumn": 8,
	"endLineNumber": 14,
	"endColumn": 18
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "268435846",
	"severity": 8,
	"message": "The import org.jacoco cannot be resolved",
	"source": "Java",
	"startLineNumber": 15,
	"startColumn": 8,
	"endLineNumber": 15,
	"endColumn": 18
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "268435846",
	"severity": 8,
	"message": "The import org.jacoco cannot be resolved",
	"source": "Java",
	"startLineNumber": 16,
	"startColumn": 8,
	"endLineNumber": 16,
	"endColumn": 18
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "268435846",
	"severity": 8,
	"message": "The import org.jacoco cannot be resolved",
	"source": "Java",
	"startLineNumber": 17,
	"startColumn": 8,
	"endLineNumber": 17,
	"endColumn": 18
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "IRuntime cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 38,
	"startColumn": 9,
	"endLineNumber": 38,
	"endColumn": 17
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "LoggerRuntime cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 38,
	"startColumn": 32,
	"endLineNumber": 38,
	"endColumn": 45
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "Instrumenter cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 41,
	"startColumn": 9,
	"endLineNumber": 41,
	"endColumn": 21
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "Instrumenter cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 41,
	"startColumn": 34,
	"endLineNumber": 41,
	"endColumn": 46
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "RuntimeData cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 75,
	"startColumn": 9,
	"endLineNumber": 75,
	"endColumn": 20
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "RuntimeData cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 75,
	"startColumn": 32,
	"endLineNumber": 75,
	"endColumn": 43
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "ExecutionDataStore cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 113,
	"startColumn": 9,
	"endLineNumber": 113,
	"endColumn": 27
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "ExecutionDataStore cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 113,
	"startColumn": 48,
	"endLineNumber": 113,
	"endColumn": 66
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "SessionInfoStore cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 114,
	"startColumn": 9,
	"endLineNumber": 114,
	"endColumn": 25
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "SessionInfoStore cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 114,
	"startColumn": 45,
	"endLineNumber": 114,
	"endColumn": 61
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "CoverageBuilder cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 119,
	"startColumn": 9,
	"endLineNumber": 119,
	"endColumn": 24
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "CoverageBuilder cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 119,
	"startColumn": 47,
	"endLineNumber": 119,
	"endColumn": 62
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "Analyzer cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 120,
	"startColumn": 9,
	"endLineNumber": 120,
	"endColumn": 17
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "Analyzer cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 120,
	"startColumn": 33,
	"endLineNumber": 120,
	"endColumn": 41
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "IClassCoverage cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 129,
	"startColumn": 14,
	"endLineNumber": 129,
	"endColumn": 28
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "ICounter cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 147,
	"startColumn": 55,
	"endLineNumber": 147,
	"endColumn": 63
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "67108967",
	"severity": 4,
	"message": "The method newInstance() from the type Class<capture#3-of ?> is deprecated",
	"source": "Java",
	"startLineNumber": 98,
	"startColumn": 33,
	"endLineNumber": 98,
	"endColumn": 44,
	"tags": [
		2
	]
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "536871362",
	"severity": 2,
	"message": "TODO Auto-generated catch block",
	"source": "Java",
	"startLineNumber": 124,
	"startColumn": 7,
	"endLineNumber": 124,
	"endColumn": 38
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory2.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "268435846",
	"severity": 8,
	"message": "The import org.jacoco cannot be resolved",
	"source": "Java",
	"startLineNumber": 8,
	"startColumn": 8,
	"endLineNumber": 8,
	"endColumn": 18
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory2.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "268435846",
	"severity": 8,
	"message": "The import org.jacoco cannot be resolved",
	"source": "Java",
	"startLineNumber": 9,
	"startColumn": 8,
	"endLineNumber": 9,
	"endColumn": 18
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory2.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "268435846",
	"severity": 8,
	"message": "The import org.jacoco cannot be resolved",
	"source": "Java",
	"startLineNumber": 10,
	"startColumn": 8,
	"endLineNumber": 10,
	"endColumn": 18
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory2.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "268435846",
	"severity": 8,
	"message": "The import org.jacoco cannot be resolved",
	"source": "Java",
	"startLineNumber": 11,
	"startColumn": 8,
	"endLineNumber": 11,
	"endColumn": 18
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory2.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "268435846",
	"severity": 8,
	"message": "The import org.jacoco cannot be resolved",
	"source": "Java",
	"startLineNumber": 12,
	"startColumn": 8,
	"endLineNumber": 12,
	"endColumn": 18
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory2.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "268435846",
	"severity": 8,
	"message": "The import org.jacoco cannot be resolved",
	"source": "Java",
	"startLineNumber": 13,
	"startColumn": 8,
	"endLineNumber": 13,
	"endColumn": 18
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory2.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "268435846",
	"severity": 8,
	"message": "The import org.jacoco cannot be resolved",
	"source": "Java",
	"startLineNumber": 14,
	"startColumn": 8,
	"endLineNumber": 14,
	"endColumn": 18
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory2.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "268435846",
	"severity": 8,
	"message": "The import org.jacoco cannot be resolved",
	"source": "Java",
	"startLineNumber": 15,
	"startColumn": 8,
	"endLineNumber": 15,
	"endColumn": 18
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory2.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "268435846",
	"severity": 8,
	"message": "The import org.jacoco cannot be resolved",
	"source": "Java",
	"startLineNumber": 16,
	"startColumn": 8,
	"endLineNumber": 16,
	"endColumn": 18
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory2.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "268435846",
	"severity": 8,
	"message": "The import org.jacoco cannot be resolved",
	"source": "Java",
	"startLineNumber": 17,
	"startColumn": 8,
	"endLineNumber": 17,
	"endColumn": 18
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory2.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "IRuntime cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 44,
	"startColumn": 9,
	"endLineNumber": 44,
	"endColumn": 17
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory2.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "LoggerRuntime cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 44,
	"startColumn": 32,
	"endLineNumber": 44,
	"endColumn": 45
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory2.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "Instrumenter cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 45,
	"startColumn": 9,
	"endLineNumber": 45,
	"endColumn": 21
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory2.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "Instrumenter cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 45,
	"startColumn": 34,
	"endLineNumber": 45,
	"endColumn": 46
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory2.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "RuntimeData cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 52,
	"startColumn": 9,
	"endLineNumber": 52,
	"endColumn": 20
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory2.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "RuntimeData cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 52,
	"startColumn": 32,
	"endLineNumber": 52,
	"endColumn": 43
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory2.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "ExecutionDataStore cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 88,
	"startColumn": 9,
	"endLineNumber": 88,
	"endColumn": 27
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory2.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "ExecutionDataStore cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 88,
	"startColumn": 48,
	"endLineNumber": 88,
	"endColumn": 66
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory2.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "SessionInfoStore cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 89,
	"startColumn": 9,
	"endLineNumber": 89,
	"endColumn": 25
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory2.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "SessionInfoStore cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 89,
	"startColumn": 45,
	"endLineNumber": 89,
	"endColumn": 61
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory2.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "CoverageBuilder cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 92,
	"startColumn": 9,
	"endLineNumber": 92,
	"endColumn": 24
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory2.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "CoverageBuilder cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 92,
	"startColumn": 47,
	"endLineNumber": 92,
	"endColumn": 62
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory2.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "Analyzer cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 93,
	"startColumn": 9,
	"endLineNumber": 93,
	"endColumn": 17
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory2.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "Analyzer cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 93,
	"startColumn": 33,
	"endLineNumber": 93,
	"endColumn": 41
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory2.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "IClassCoverage cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 101,
	"startColumn": 14,
	"endLineNumber": 101,
	"endColumn": 28
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory2.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "ICounter cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 117,
	"startColumn": 55,
	"endLineNumber": 117,
	"endColumn": 63
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory2.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "570425421",
	"severity": 4,
	"message": "The value of the field CoverageFactory2.methodsArr is not used",
	"source": "Java",
	"startLineNumber": 31,
	"startColumn": 19,
	"endLineNumber": 31,
	"endColumn": 29,
	"tags": [
		1
	]
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactory2.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "67108967",
	"severity": 4,
	"message": "The method newInstance() from the type Class<capture#3-of ?> is deprecated",
	"source": "Java",
	"startLineNumber": 75,
	"startColumn": 33,
	"endLineNumber": 75,
	"endColumn": 44,
	"tags": [
		2
	]
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactoryFor3D.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "268435846",
	"severity": 8,
	"message": "The import org.jacoco cannot be resolved",
	"source": "Java",
	"startLineNumber": 9,
	"startColumn": 8,
	"endLineNumber": 9,
	"endColumn": 18
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactoryFor3D.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "268435846",
	"severity": 8,
	"message": "The import org.jacoco cannot be resolved",
	"source": "Java",
	"startLineNumber": 10,
	"startColumn": 8,
	"endLineNumber": 10,
	"endColumn": 18
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactoryFor3D.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "268435846",
	"severity": 8,
	"message": "The import org.jacoco cannot be resolved",
	"source": "Java",
	"startLineNumber": 11,
	"startColumn": 8,
	"endLineNumber": 11,
	"endColumn": 18
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactoryFor3D.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "268435846",
	"severity": 8,
	"message": "The import org.jacoco cannot be resolved",
	"source": "Java",
	"startLineNumber": 12,
	"startColumn": 8,
	"endLineNumber": 12,
	"endColumn": 18
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactoryFor3D.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "268435846",
	"severity": 8,
	"message": "The import org.jacoco cannot be resolved",
	"source": "Java",
	"startLineNumber": 13,
	"startColumn": 8,
	"endLineNumber": 13,
	"endColumn": 18
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactoryFor3D.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "268435846",
	"severity": 8,
	"message": "The import org.jacoco cannot be resolved",
	"source": "Java",
	"startLineNumber": 14,
	"startColumn": 8,
	"endLineNumber": 14,
	"endColumn": 18
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactoryFor3D.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "268435846",
	"severity": 8,
	"message": "The import org.jacoco cannot be resolved",
	"source": "Java",
	"startLineNumber": 15,
	"startColumn": 8,
	"endLineNumber": 15,
	"endColumn": 18
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactoryFor3D.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "268435846",
	"severity": 8,
	"message": "The import org.jacoco cannot be resolved",
	"source": "Java",
	"startLineNumber": 16,
	"startColumn": 8,
	"endLineNumber": 16,
	"endColumn": 18
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactoryFor3D.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "268435846",
	"severity": 8,
	"message": "The import org.jacoco cannot be resolved",
	"source": "Java",
	"startLineNumber": 17,
	"startColumn": 8,
	"endLineNumber": 17,
	"endColumn": 18
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactoryFor3D.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "268435846",
	"severity": 8,
	"message": "The import org.jacoco cannot be resolved",
	"source": "Java",
	"startLineNumber": 18,
	"startColumn": 8,
	"endLineNumber": 18,
	"endColumn": 18
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactoryFor3D.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "IRuntime cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 45,
	"startColumn": 9,
	"endLineNumber": 45,
	"endColumn": 17
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactoryFor3D.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "LoggerRuntime cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 45,
	"startColumn": 32,
	"endLineNumber": 45,
	"endColumn": 45
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactoryFor3D.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "Instrumenter cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 46,
	"startColumn": 9,
	"endLineNumber": 46,
	"endColumn": 21
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactoryFor3D.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "Instrumenter cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 46,
	"startColumn": 34,
	"endLineNumber": 46,
	"endColumn": 46
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactoryFor3D.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "RuntimeData cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 53,
	"startColumn": 9,
	"endLineNumber": 53,
	"endColumn": 20
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactoryFor3D.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "RuntimeData cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 53,
	"startColumn": 32,
	"endLineNumber": 53,
	"endColumn": 43
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactoryFor3D.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "ExecutionDataStore cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 90,
	"startColumn": 9,
	"endLineNumber": 90,
	"endColumn": 27
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactoryFor3D.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "ExecutionDataStore cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 90,
	"startColumn": 48,
	"endLineNumber": 90,
	"endColumn": 66
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactoryFor3D.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "SessionInfoStore cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 91,
	"startColumn": 9,
	"endLineNumber": 91,
	"endColumn": 25
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactoryFor3D.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "SessionInfoStore cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 91,
	"startColumn": 45,
	"endLineNumber": 91,
	"endColumn": 61
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactoryFor3D.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "CoverageBuilder cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 94,
	"startColumn": 9,
	"endLineNumber": 94,
	"endColumn": 24
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactoryFor3D.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "CoverageBuilder cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 94,
	"startColumn": 47,
	"endLineNumber": 94,
	"endColumn": 62
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactoryFor3D.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "Analyzer cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 95,
	"startColumn": 9,
	"endLineNumber": 95,
	"endColumn": 17
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactoryFor3D.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "Analyzer cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 95,
	"startColumn": 33,
	"endLineNumber": 95,
	"endColumn": 41
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactoryFor3D.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "IClassCoverage cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 103,
	"startColumn": 14,
	"endLineNumber": 103,
	"endColumn": 28
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactoryFor3D.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "ICounter cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 119,
	"startColumn": 55,
	"endLineNumber": 119,
	"endColumn": 63
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactoryFor3D.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "570425421",
	"severity": 4,
	"message": "The value of the field CoverageFactoryFor3D.methodsArr is not used",
	"source": "Java",
	"startLineNumber": 32,
	"startColumn": 19,
	"endLineNumber": 32,
	"endColumn": 29,
	"tags": [
		1
	]
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/factory/CoverageFactoryFor3D.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "67108967",
	"severity": 4,
	"message": "The method newInstance() from the type Class<capture#3-of ?> is deprecated",
	"source": "Java",
	"startLineNumber": 76,
	"startColumn": 33,
	"endLineNumber": 76,
	"endColumn": 44,
	"tags": [
		2
	]
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/report/ReportGenerator.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "268435846",
	"severity": 8,
	"message": "The import org.jacoco cannot be resolved",
	"source": "Java",
	"startLineNumber": 18,
	"startColumn": 8,
	"endLineNumber": 18,
	"endColumn": 18
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/report/ReportGenerator.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "268435846",
	"severity": 8,
	"message": "The import org.jacoco cannot be resolved",
	"source": "Java",
	"startLineNumber": 19,
	"startColumn": 8,
	"endLineNumber": 19,
	"endColumn": 18
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/report/ReportGenerator.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "268435846",
	"severity": 8,
	"message": "The import org.jacoco cannot be resolved",
	"source": "Java",
	"startLineNumber": 20,
	"startColumn": 8,
	"endLineNumber": 20,
	"endColumn": 18
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/report/ReportGenerator.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "268435846",
	"severity": 8,
	"message": "The import org.jacoco cannot be resolved",
	"source": "Java",
	"startLineNumber": 21,
	"startColumn": 8,
	"endLineNumber": 21,
	"endColumn": 18
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/report/ReportGenerator.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "268435846",
	"severity": 8,
	"message": "The import org.jacoco cannot be resolved",
	"source": "Java",
	"startLineNumber": 22,
	"startColumn": 8,
	"endLineNumber": 22,
	"endColumn": 18
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/report/ReportGenerator.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "268435846",
	"severity": 8,
	"message": "The import org.jacoco cannot be resolved",
	"source": "Java",
	"startLineNumber": 23,
	"startColumn": 8,
	"endLineNumber": 23,
	"endColumn": 18
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/report/ReportGenerator.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "268435846",
	"severity": 8,
	"message": "The import org.jacoco cannot be resolved",
	"source": "Java",
	"startLineNumber": 24,
	"startColumn": 8,
	"endLineNumber": 24,
	"endColumn": 18
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/report/ReportGenerator.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "268435846",
	"severity": 8,
	"message": "The import org.jacoco cannot be resolved",
	"source": "Java",
	"startLineNumber": 25,
	"startColumn": 8,
	"endLineNumber": 25,
	"endColumn": 18
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/report/ReportGenerator.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "ExecFileLoader cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 44,
	"startColumn": 10,
	"endLineNumber": 44,
	"endColumn": 24
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/report/ReportGenerator.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "IBundleCoverage cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 76,
	"startColumn": 9,
	"endLineNumber": 76,
	"endColumn": 24
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/report/ReportGenerator.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "67108984",
	"severity": 8,
	"message": "The method analyzeStructure() from the type ReportGenerator refers to the missing type IBundleCoverage",
	"source": "Java",
	"startLineNumber": 76,
	"startColumn": 42,
	"endLineNumber": 76,
	"endColumn": 58
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/report/ReportGenerator.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "IBundleCoverage cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 82,
	"startColumn": 34,
	"endLineNumber": 82,
	"endColumn": 49
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/report/ReportGenerator.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "HTMLFormatter cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 87,
	"startColumn": 9,
	"endLineNumber": 87,
	"endColumn": 22
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/report/ReportGenerator.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "HTMLFormatter cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 87,
	"startColumn": 43,
	"endLineNumber": 87,
	"endColumn": 56
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/report/ReportGenerator.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "IReportVisitor cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 88,
	"startColumn": 9,
	"endLineNumber": 88,
	"endColumn": 23
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/report/ReportGenerator.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "FileMultiReportOutput cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 89,
	"startColumn": 24,
	"endLineNumber": 89,
	"endColumn": 45
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/report/ReportGenerator.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "ExecFileLoader cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 94,
	"startColumn": 21,
	"endLineNumber": 94,
	"endColumn": 35
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/report/ReportGenerator.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "ExecFileLoader cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 95,
	"startColumn": 5,
	"endLineNumber": 95,
	"endColumn": 19
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/report/ReportGenerator.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "DirectorySourceFileLocator cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 99,
	"startColumn": 43,
	"endLineNumber": 99,
	"endColumn": 69
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/report/ReportGenerator.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "ExecFileLoader cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 109,
	"startColumn": 3,
	"endLineNumber": 109,
	"endColumn": 17
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/report/ReportGenerator.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "ExecFileLoader cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 109,
	"startColumn": 24,
	"endLineNumber": 109,
	"endColumn": 38
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/report/ReportGenerator.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "ExecFileLoader cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 110,
	"startColumn": 3,
	"endLineNumber": 110,
	"endColumn": 17
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/report/ReportGenerator.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "IBundleCoverage cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 113,
	"startColumn": 10,
	"endLineNumber": 113,
	"endColumn": 25
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/report/ReportGenerator.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "CoverageBuilder cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 114,
	"startColumn": 9,
	"endLineNumber": 114,
	"endColumn": 24
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/report/ReportGenerator.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "CoverageBuilder cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 114,
	"startColumn": 47,
	"endLineNumber": 114,
	"endColumn": 62
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/report/ReportGenerator.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "Analyzer cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 115,
	"startColumn": 9,
	"endLineNumber": 115,
	"endColumn": 17
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/report/ReportGenerator.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "Analyzer cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 115,
	"startColumn": 33,
	"endLineNumber": 115,
	"endColumn": 41
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/coverage/report/ReportGenerator.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777218",
	"severity": 8,
	"message": "ExecFileLoader cannot be resolved to a type",
	"source": "Java",
	"startLineNumber": 116,
	"startColumn": 5,
	"endLineNumber": 116,
	"endColumn": 19
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/test/simulations/art_9partition/TestFor9partition.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "268435846",
	"severity": 8,
	"message": "The import org.junit cannot be resolved",
	"source": "Java",
	"startLineNumber": 7,
	"startColumn": 8,
	"endLineNumber": 7,
	"endColumn": 17
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/test/simulations/art_9partition/TestFor9partition.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "536870973",
	"severity": 4,
	"message": "The value of the local variable c is not used",
	"source": "Java",
	"startLineNumber": 24,
	"startColumn": 7,
	"endLineNumber": 24,
	"endColumn": 8,
	"tags": [
		1
	]
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/test/simulations/art_9partition/TestFor9partition.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "536870973",
	"severity": 4,
	"message": "The value of the local variable ct is not used",
	"source": "Java",
	"startLineNumber": 120,
	"startColumn": 7,
	"endLineNumber": 120,
	"endColumn": 9,
	"tags": [
		1
	]
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/testPrograms/Fault_6d/PntLinePos_Err.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "536871240",
	"severity": 8,
	"message": "The declared package \"Fault_6d\" does not match the expected package \"testPrograms.Fault_6d\"",
	"source": "Java",
	"startLineNumber": 1,
	"startColumn": 9,
	"endLineNumber": 1,
	"endColumn": 17
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/testPrograms/Fault_6d/PntLinePos.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "536871240",
	"severity": 8,
	"message": "The declared package \"Fault_6d\" does not match the expected package \"testPrograms.Fault_6d\"",
	"source": "Java",
	"startLineNumber": 1,
	"startColumn": 9,
	"endLineNumber": 1,
	"endColumn": 17
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/testPrograms/Fault_6d/PntLinePos.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "67109264",
	"severity": 8,
	"message": "The type PntLinePos must implement the inherited abstract method PUT_6D.isCorrect(int, int, int, int, int, int)",
	"source": "Java",
	"startLineNumber": 10,
	"startColumn": 14,
	"endLineNumber": 10,
	"endColumn": 24
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/testPrograms/Fault_6d/PntLinePos.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "67109498",
	"severity": 8,
	"message": "The method producesError(int, int, int, int, int, int) of type PntLinePos must override or implement a supertype method",
	"source": "Java",
	"startLineNumber": 47,
	"startColumn": 17,
	"endLineNumber": 47,
	"endColumn": 78
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/testPrograms/Fault_6d/PntLinePos.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "536871362",
	"severity": 2,
	"message": "TODO Auto-generated method stub",
	"source": "Java",
	"startLineNumber": 48,
	"startColumn": 6,
	"endLineNumber": 48,
	"endColumn": 37
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/testPrograms/Fault_6d/PntLinePos.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "536871362",
	"severity": 2,
	"message": "TODO Auto-generated method stub",
	"source": "Java",
	"startLineNumber": 59,
	"startColumn": 6,
	"endLineNumber": 59,
	"endColumn": 37
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/testPrograms/Fault_8d/PntTrianglePos.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "536871240",
	"severity": 8,
	"message": "The declared package \"Fault_8d\" does not match the expected package \"testPrograms.Fault_8d\"",
	"source": "Java",
	"startLineNumber": 1,
	"startColumn": 9,
	"endLineNumber": 1,
	"endColumn": 17
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/testPrograms/Fault_8d/PntTrianglePos.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "67109264",
	"severity": 8,
	"message": "The type PntTrianglePos must implement the inherited abstract method PUT_8D.isCorrect(int, int, int, int, int, int, int, int)",
	"source": "Java",
	"startLineNumber": 8,
	"startColumn": 14,
	"endLineNumber": 8,
	"endColumn": 28
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/testPrograms/Fault_8d/PntTrianglePos.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "67109498",
	"severity": 8,
	"message": "The method producesError(int, int, int, int, int, int, int, int) of type PntTrianglePos must override or implement a supertype method",
	"source": "Java",
	"startLineNumber": 122,
	"startColumn": 17,
	"endLineNumber": 122,
	"endColumn": 94
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/testPrograms/Fault_8d/PntTrianglePos.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "536871362",
	"severity": 2,
	"message": "TODO Auto-generated method stub",
	"source": "Java",
	"startLineNumber": 123,
	"startColumn": 6,
	"endLineNumber": 123,
	"endColumn": 37
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/testPrograms/Fault_8d/PntTrianglePos.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "536871362",
	"severity": 2,
	"message": "TODO Auto-generated method stub",
	"source": "Java",
	"startLineNumber": 135,
	"startColumn": 6,
	"endLineNumber": 135,
	"endColumn": 37
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/util/HilbertCurve2.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777539",
	"severity": 8,
	"message": "The type RefObject is already defined",
	"source": "Java",
	"startLineNumber": 968,
	"startColumn": 13,
	"endLineNumber": 968,
	"endColumn": 22
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/util/HilbertCurve2.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777788",
	"severity": 4,
	"message": "ArrayList is a raw type. References to generic type ArrayList<E> should be parameterized",
	"source": "Java",
	"startLineNumber": 209,
	"startColumn": 15,
	"endLineNumber": 209,
	"endColumn": 24
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/util/HilbertCurve2.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777788",
	"severity": 4,
	"message": "ArrayList is a raw type. References to generic type ArrayList<E> should be parameterized",
	"source": "Java",
	"startLineNumber": 215,
	"startColumn": 3,
	"endLineNumber": 215,
	"endColumn": 22
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/util/HilbertCurve2.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777788",
	"severity": 4,
	"message": "ArrayList is a raw type. References to generic type ArrayList<E> should be parameterized",
	"source": "Java",
	"startLineNumber": 215,
	"startColumn": 33,
	"endLineNumber": 215,
	"endColumn": 52
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/util/HilbertCurve2.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777747",
	"severity": 4,
	"message": "Type safety: The method add(Object) belongs to the raw type ArrayList. References to generic type ArrayList<E> should be parameterized",
	"source": "Java",
	"startLineNumber": 284,
	"startColumn": 3,
	"endLineNumber": 284,
	"endColumn": 23
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/util/HilbertCurve2.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "16777747",
	"severity": 4,
	"message": "Type safety: The method add(Object) belongs to the raw type ArrayList. References to generic type ArrayList<E> should be parameterized",
	"source": "Java",
	"startLineNumber": 285,
	"startColumn": 3,
	"endLineNumber": 285,
	"endColumn": 23
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/util/HilbertCurve2.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "536870973",
	"severity": 4,
	"message": "The value of the local variable OrderOfHilbert is not used",
	"source": "Java",
	"startLineNumber": 385,
	"startColumn": 13,
	"endLineNumber": 385,
	"endColumn": 27,
	"tags": [
		1
	]
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/util/HilbertCurve2.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "536870973",
	"severity": 4,
	"message": "The value of the local variable mask is not used",
	"source": "Java",
	"startLineNumber": 402,
	"startColumn": 8,
	"endLineNumber": 402,
	"endColumn": 12,
	"tags": [
		1
	]
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/util/HilbertCurve2.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "536870973",
	"severity": 4,
	"message": "The value of the local variable element is not used",
	"source": "Java",
	"startLineNumber": 406,
	"startColumn": 8,
	"endLineNumber": 406,
	"endColumn": 15,
	"tags": [
		1
	]
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/util/HilbertCurve2.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "536870973",
	"severity": 4,
	"message": "The value of the local variable OrderOfHilbert is not used",
	"source": "Java",
	"startLineNumber": 538,
	"startColumn": 13,
	"endLineNumber": 538,
	"endColumn": 27,
	"tags": [
		1
	]
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/util/HilbertCurve2.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "536870973",
	"severity": 4,
	"message": "The value of the local variable mask is not used",
	"source": "Java",
	"startLineNumber": 556,
	"startColumn": 8,
	"endLineNumber": 556,
	"endColumn": 12,
	"tags": [
		1
	]
},{
	"resource": "/C:/Users/<USER>/Desktop/java workspace/ART-ORBO/src/util/HilbertCurve2.java",
	"owner": "_generated_diagnostic_collection_name_#3",
	"code": "536870973",
	"severity": 4,
	"message": "The value of the local variable element is not used",
	"source": "Java",
	"startLineNumber": 560,
	"startColumn": 8,
	"endLineNumber": 560,
	"endColumn": 15,
	"tags": [
		1
	]
}]



also the locations should be relative not definate ,so that when i use a different computer i will not require to make new changes for location

