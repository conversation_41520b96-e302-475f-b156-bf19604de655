@echo off
echo Building ART-ORBO Project...

REM Clean previous build
if exist bin rmdir /s /q bin
mkdir bin

REM Compile the entire project
echo Compiling source files...
javac -cp "libs/*" -d bin -sourcepath src src/a_main/MainMethod.java

if %ERRORLEVEL% EQU 0 (
    echo ✅ Build successful!
    echo.
    echo To run the main program:
    echo java -cp "libs/*;bin" a_main.MainMethod
    echo.
    echo To run tests:
    echo java -cp "libs/*;bin" a_main.TestTriangle
    echo.
    echo To run coverage factory:
    echo java -cp "libs/*;bin" coverage.factory.CoverageFactory2
) else (
    echo ❌ Build failed!
    exit /b 1
)
